/* Import Bootstrap styles */
@import 'bootstrap/dist/css/bootstrap.min.css';

/* Custom styles */
body {
  font-family: 'Roboto Condensed', sans-serif;
}

/* Override Bootstrap font family */
* {
  font-family: 'Roboto Condensed', sans-serif !important;
}

/* Narandžasta boja za tekst */
.text-orange {
  color: #f38630 !important;
}

/* Narandžasta pozadina */
.bg-orange {
  background-color: #f38630 !important;
}

/* Sidebar stilovi */
.sidebar {
  max-height: calc(100vh - 2rem);
  overflow-y: auto;
  position: sticky;
  top: 1rem;
}

.filter-group {
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 1rem;
}

.filter-group:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.form-check-group {
  max-height: 200px;
  overflow-y: auto;
}

.form-check-group .form-check {
  margin-bottom: 0.5rem;
}

.filter-results {
  border: 2px solid #f38630;
}

/* <PERSON><PERSON>a stilovi */
.table-container {
  border-radius: 0.5rem;
  overflow: hidden;
}

.table thead th {
  background-color: #f38630;
  color: white;
  font-weight: 600;
  border: none;
}

.table tbody tr:hover {
  background-color: rgba(243, 134, 48, 0.1);
}

/* Button stilovi */
.btn-primary {
  background-color: #f38630;
  border-color: #f38630;
}

.btn-primary:hover, .btn-primary:focus {
  background-color: #e67e28;
  border-color: #e67e28;
}

.btn-primary:disabled {
  background-color: #f38630;
  border-color: #f38630;
  opacity: 0.5;
}

.btn-outline-primary {
  color: #f38630;
  border-color: #f38630;
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
  background-color: #f38630;
  border-color: #f38630;
  color: white;
}

.btn-check:checked + .btn-outline-primary {
  background-color: #f38630;
  border-color: #f38630;
  color: white;
}

/* Results area */
.results-area {
  min-height: 600px;
}

/* Responsive design */
@media (max-width: 991.98px) {
  .sidebar {
    position: static;
    max-height: none;
    margin-bottom: 2rem;
  }

  .filter-group {
    margin-bottom: 1.5rem;
  }

  /* Fiksiranje problema sa padajućim menijem na tablet uređajima */
  .results-area .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .results-area .d-flex.align-items-center.gap-2 {
    justify-content: flex-start;
  }
}

@media (max-width: 767.98px) {
  .sidebar {
    padding: 1rem;
  }

  .results-area {
    padding: 1rem;
    min-height: 400px;
  }

  /* Fiksiranje problema sa padajućim menijem na mobilnim uređajima */
  .results-area .d-flex.justify-content-between {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 1rem;
  }

  .results-area .d-flex.align-items-center.gap-2 {
    width: 100%;
    flex-direction: column;
    align-items: flex-start !important;
    gap: 0.5rem;
  }

  .results-area .form-select {
    width: 100% !important;
    max-width: none !important;
  }

  /* Izuzetak za mobilni paginacijski select */
  .results-area .mobile-pagination-select {
    width: auto !important;
    min-width: 60px !important;
  }

  /* Forsiranje da mobilni paginacijski red ostane u jednom redu */
  .results-area .mobile-pagination-row {
    flex-direction: row !important;
    align-items: center !important;
  }

  /* Specifičan override za mobilni paginacijski red da tekst bude u ravni sa select-om */
  .results-area .d-flex.mobile-pagination-row {
    align-items: center !important;
  }

  /* Još specifičniji override sa višom specifičnošću */
  .results-area .d-flex.align-items-center.gap-2.mobile-pagination-row {
    align-items: center !important;
    flex-direction: row !important;
  }

  /* Smanjivanje font-a za tabelu */
  .table {
    font-size: 0.85rem;
  }

  .table td, .table th {
    padding: 0.5rem 0.25rem;
  }

  /* Sticky prva kolona na mobilnim uređajima */
  .table th:first-child,
  .table td:first-child {
    position: sticky;
    left: 0;
    z-index: 1;
    background-color: white;
    border-right: 1px solid #dee2e6;
    min-width: 120px;
  }

  .table th:first-child {
    background-color: #f38630;
    color: white;
  }

  .table-striped tbody tr:nth-of-type(odd) td:first-child {
    background-color: #f8f9fa;
  }

  .table-hover tbody tr:hover td:first-child {
    background-color: #fff3e0;
  }

  /* Poboljšanje paginacije na mobilnim uređajima */
  .d-flex.justify-content-center.align-items-center.gap-2 {
    flex-direction: column;
    gap: 1rem;
  }

  .d-flex.justify-content-center.align-items-center.gap-2 .btn {
    width: 100%;
    max-width: 200px;
  }

  /* Poboljšanje sidebar-a na mobilnim uređajima */
  .filter-group .form-check-group {
    max-height: 150px;
  }

  .filter-group .btn-group-vertical {
    width: 100%;
  }

  .filter-group .btn-group-vertical .btn {
    text-align: left;
  }
}

/* Dodatne optimizacije za vrlo male ekrane */
@media (max-width: 575.98px) {
  .container-fluid {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .sidebar, .results-area {
    padding: 0.75rem;
  }

  .table-responsive {
    border-radius: 0.375rem;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Indikator za horizontalno skrolovanje - sticky na dnu */
  .table-responsive::after {
    content: "⟷ Prevucite za više kolona";
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1030;
    display: block;
    text-align: center;
    font-size: 0.8rem;
    font-weight: 500;
    color: white;
    padding: 1.5rem 1rem;
    background: linear-gradient(135deg, #f38630 0%, #e67e28 100%);
    border-top: 2px solid #e67e28;
    box-shadow: 0 -4px 20px rgba(243, 134, 48, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }

  .table-responsive::after:hover {
    background: linear-gradient(135deg, #e67e28 0%, #d4721f 100%);
    transform: translateY(-2px);
    box-shadow: 0 -6px 25px rgba(243, 134, 48, 0.4);
  }

  /* Smanjivanje veličine dugmića */
  .btn-sm {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }

  /* Optimizacija filter rezultata */
  .filter-results {
    padding: 0.75rem;
  }

  .filter-results .badge {
    font-size: 0.75rem;
  }
}

/* Scrollbar stilovi za sidebar */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb {
  background: #f38630;
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: #e67e28;
}

/* Form kontrole */
.form-control:focus, .form-select:focus {
  border-color: #f38630;
  box-shadow: 0 0 0 0.2rem rgba(243, 134, 48, 0.25);
}

/* Fiksiranje problema sa padajućim menijem */
.form-select {
  background-position: right 0.75rem center;
}

/* Osiguravanje da se dropdown ne prelije preko granica */
.results-area .form-select {
  max-width: 100%;
  overflow: hidden;
}

/* Custom outline select za mobilni sortiranje */
.form-select-outline-primary {
  background-color: transparent;
  border: 1px solid #f38630;
  color: #f38630;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23f38630' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
}

.form-select-outline-primary:focus {
  border-color: #f38630;
  box-shadow: 0 0 0 0.2rem rgba(243, 134, 48, 0.25);
  background-color: transparent;
  color: #f38630;
}

/* Course type icons */
.course-type-icon {
  font-size: 1.1rem;
  flex-shrink: 0;
}

/* Sort controls - desktop verzija (ostaje sa desne strane) */
.sort-controls {
  /* Na desktop-u ostaje normalno sa desne strane */
}

/* Dodatne responsive klase */
@media (max-width: 768px) {
  /* Sort controls - mobilna verzija */
  .sort-controls {
    width: 100% !important;
    justify-content: flex-start !important;
    margin-top: 0.5rem;
  }

  .sort-controls .form-select {
    flex-grow: 1;
    max-width: none;
  }

  .w-md-auto {
    width: 100% !important;
  }

  .flex-grow-1 {
    flex-grow: 1 !important;
  }

  .text-nowrap {
    white-space: nowrap;
  }
}

/* Breadcrumb stilovi */
.breadcrumb {
  background-color: transparent;
  padding: 0;
  margin-bottom: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: ">";
  color: #6c757d;
}

.breadcrumb-item.active {
  color: #6c757d;
}

/* Badge stilovi */
.badge {
  font-size: 0.875rem;
}

/* Mobilni slide-out filter panel */
.mobile-filter-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1040;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-filter-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Sprečava skrolovanje body-ja kada su mobilni filtri otvoreni */
body.mobile-filter-open {
  overflow: hidden;
}

/* Dropdown hover efekti */
@media (min-width: 992px) {
  .navbar-nav .dropdown:hover .dropdown-menu {
    display: block;
    margin-top: 15px;
  }

  .navbar-nav .dropdown .dropdown-menu {
    margin-top: 15px;
  }
}

/* Poboljšanja za dropdown menuje */
.dropdown-menu {
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 0.375rem;
}

.dropdown-item:hover,
.dropdown-item:focus {
  background-color: rgba(243, 134, 48, 0.1);
  color: #f38630;
}

/* Mega menu styles - globalni stilovi */
.navbar {
  position: relative;
}

/* Mega menu dropdown positioning */
.navbar-nav .dropdown {
  position: relative;
}

.navbar-nav .dropdown.mega-menu-dropdown {
  position: static;
}

.mega-menu {
  position: absolute !important;
  top: 100% !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 100% !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  margin-top: 0 !important;
  z-index: 1060 !important;
}

/* Responsive max-width za mega menu da odgovara Bootstrap container-u */
@media (min-width: 576px) {
  .mega-menu {
    max-width: 540px !important;
  }
}

@media (min-width: 768px) {
  .mega-menu {
    max-width: 720px !important;
  }
}

@media (min-width: 992px) {
  .mega-menu {
    max-width: 960px !important;
  }
}

@media (min-width: 1200px) {
  .mega-menu {
    max-width: 1140px !important;
  }
}

@media (min-width: 1400px) {
  .mega-menu {
    max-width: 1320px !important;
  }
}

.mega-menu .container {
  width: 100%;
  margin: 0 auto;
  padding-left: 15px;
  padding-right: 15px;
}

.mega-menu-content {
  padding: 0;
}

.mega-menu-column h6 {
  color: #f38630;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #f38630;
}

.mega-menu-column .dropdown-item {
  padding: 0.5rem 0;
  border: none;
  color: #333;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.mega-menu-column .dropdown-item span {
  margin-right: 0.5rem;
  display: inline-flex;
  align-items: center;
}

.mega-menu-column .dropdown-item:hover {
  background: transparent;
  color: #f38630;
  padding-left: 0.5rem;
}

/* Regular dropdown positioning */
.dropdown-menu:not(.mega-menu) {
  min-width: 250px;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
}

/* Navbar fiksne visine i stilovi */
.navbar {
  min-height: 70px;
}

/* Navbar links uppercase - samo glavni linkovi, ne mega menu */
.navbar-nav > .nav-item > .nav-link {
  text-transform: uppercase !important;
  color: white !important;
  font-size: 12px !important;
  transition: color 0.3s ease;
  padding-top: 23px !important;
  padding-bottom: 20px !important;
  height: auto !important;
}

/* Navbar links gradient hover effect - pokriva celu navbar visinu */
.navbar-nav > .nav-item > .nav-link:hover,
.navbar-nav > .nav-item > .nav-link:focus,
.navbar-nav > .nav-item > .nav-link:active,
.navbar-nav > .nav-item > .nav-link.active {
  background-image: linear-gradient(to bottom, #ff970c 0%, #ff7319 100%) !important;
  color: white !important;
  margin-top: -23px !important;
  margin-bottom: -27px !important;
  padding-top: 46px !important;
  padding-bottom: 47px !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Narandžasta traka na dnu navbar-a */
.navbar.navbar-dark.bg-dark {
  border-bottom: 3px solid #ff7319;
}

/* Fiksni navbar */
.navbar.fixed-top {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1030 !important;
  border-bottom: 3px solid #ff7319;
}

@media (max-width: 991px) {
  .navbar-nav .dropdown {
    position: relative;
  }

  .mega-menu {
    position: relative !important;
    width: auto !important;
    max-width: none !important;
    box-shadow: none !important;
    transform: none !important;
    top: auto !important;
    left: auto !important;
  }

  .mega-menu-content {
    padding: 0;
  }

  .mega-menu-column {
    margin-bottom: 1.5rem;
  }
}

.mobile-filter-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 85%;
  max-width: 400px;
  height: 100%;
  background-color: white;
  z-index: 1050;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.mobile-filter-panel.show {
  transform: translateX(0);
}

.mobile-filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.mobile-filter-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.mobile-filter-footer {
  padding: 1rem;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

/* Mobilni filter content stilovi */
.mobile-filter-content .filter-group {
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
}

.mobile-filter-content .filter-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.mobile-filter-content .form-check-group {
  max-height: 200px;
  overflow-y: auto;
}

/* Animacije */
.filter-group {
  transition: all 0.3s ease;
}

.btn {
  transition: all 0.2s ease;
}

/* Prevent body scroll when mobile filter is open */
body.mobile-filter-open {
  overflow: hidden;
}
