import * as bootstrap from 'bootstrap'

// Type declaration za window.bootstrap
declare global {
  interface Window {
    bootstrap: any
  }
}

export default defineNuxtPlugin(() => {
  // Inicijalizuj Bootstrap komponente kada se DOM učita
  if (process.client) {
    console.log('Bootstrap plugin se učitava...')

    // Funkcija za čekanje da se Bootstrap učita
    const waitForBootstrap = () => {
      return new Promise((resolve) => {
        const checkBootstrap = () => {
          if (window.bootstrap || (window as any).bootstrap) {
            console.log('Bootstrap je dostupan!')
            resolve(true)
          } else {
            console.log('Čekam da se Bootstrap učita...')
            setTimeout(checkBootstrap, 100)
          }
        }
        checkBootstrap()
      })
    }

    // Čekaj da se DOM i Bootstrap učitaju
    const initBootstrap = async () => {
      console.log('Inicijalizujem Bootstrap komponente...')
      await waitForBootstrap()

      // <PERSON><PERSON> koristi globalni Bootstrap umesto lokalnog
      if (window.bootstrap) {
        console.log('Koristim globalni Bootstrap')
        // Inicijalizuj dropdown komponente
        const dropdownElementList = document.querySelectorAll('[data-bs-toggle="dropdown"]')
        console.log(`Pronašao ${dropdownElementList.length} dropdown elemenata`)

        dropdownElementList.forEach(dropdownToggleEl => {
          new window.bootstrap.Dropdown(dropdownToggleEl)
        })

        // Inicijalizuj collapse komponente
        const collapseElementList = document.querySelectorAll('[data-bs-toggle="collapse"]')
        console.log(`Pronašao ${collapseElementList.length} collapse elemenata`)

        collapseElementList.forEach(collapseToggleEl => {
          new window.bootstrap.Collapse(collapseToggleEl, { toggle: false })
        })

        // Inicijalizuj modal komponente
        const modalElementList = document.querySelectorAll('.modal')
        console.log(`Pronašao ${modalElementList.length} modal elemenata`)

        modalElementList.forEach(modalEl => {
          new window.bootstrap.Modal(modalEl)
        })
      }
    }

    // Ako je DOM već učitan, inicijalizuj odmah
    if (document.readyState === 'loading') {
      console.log('DOM se još učitava, čekam DOMContentLoaded...')
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(initBootstrap, 500) // Duža pauza da se osigura da je Bootstrap učitan
      })
    } else {
      // DOM je već učitan, inicijalizuj odmah
      console.log('DOM je već učitan, inicijalizujem odmah...')
      setTimeout(initBootstrap, 500) // Duža pauza da se osigura da je Bootstrap učitan
    }

    // Takođe inicijalizuj nakon navigacije (za SPA)
    const router = useRouter()
    router.afterEach((to) => {
      console.log('Navigacija na:', to.path)
      setTimeout(initBootstrap, 500)
    })
  }

  return {
    provide: {
      bootstrap
    }
  }
})
