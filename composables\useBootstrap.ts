import * as bootstrap from 'bootstrap'

export const useBootstrap = () => {
  const initializeDropdowns = () => {
    if (process.client) {
      nextTick(() => {
        console.log('Inicijalizujem dropdown komponente...')

        // Uni<PERSON><PERSON> posto<PERSON> dropdown instance
        const existingDropdowns = document.querySelectorAll('[data-bs-toggle="dropdown"]')
        existingDropdowns.forEach(element => {
          const existingInstance = bootstrap.Dropdown.getInstance(element)
          if (existingInstance) {
            existingInstance.dispose()
          }
        })

        // Kreiraj nove dropdown instance
        const dropdownElementList = document.querySelectorAll('[data-bs-toggle="dropdown"]')
        const dropdownList = [...dropdownElementList].map(dropdownToggleEl => {
          return new bootstrap.Dropdown(dropdownToggleEl, {
            autoClose: true,
            boundary: 'clippingParents'
          })
        })

        console.log(`Inicijalizovano ${dropdownList.length} dropdown komponenti`)
        return dropdownList
      })
    }
  }

  const initializeCollapses = () => {
    if (process.client) {
      nextTick(() => {
        console.log('Inicijalizujem collapse komponente...')

        const collapseElementList = document.querySelectorAll('[data-bs-toggle="collapse"]')
        const collapseList = [...collapseElementList].map(collapseToggleEl => {
          const existingInstance = bootstrap.Collapse.getInstance(collapseToggleEl)
          if (existingInstance) {
            existingInstance.dispose()
          }
          
          return new bootstrap.Collapse(collapseToggleEl, {
            toggle: false
          })
        })

        console.log(`Inicijalizovano ${collapseList.length} collapse komponenti`)
        return collapseList
      })
    }
  }

  const initializeModals = () => {
    if (process.client) {
      nextTick(() => {
        console.log('Inicijalizujem modal komponente...')

        const modalElementList = document.querySelectorAll('.modal')
        const modalList = [...modalElementList].map(modalEl => {
          const existingInstance = bootstrap.Modal.getInstance(modalEl)
          if (existingInstance) {
            existingInstance.dispose()
          }
          
          return new bootstrap.Modal(modalEl)
        })

        console.log(`Inicijalizovano ${modalList.length} modal komponenti`)
        return modalList
      })
    }
  }

  const initializeAllComponents = () => {
    console.log('Inicijalizujem sve Bootstrap komponente...')
    initializeDropdowns()
    initializeCollapses()
    initializeModals()
  }

  return {
    bootstrap,
    initializeDropdowns,
    initializeCollapses,
    initializeModals,
    initializeAllComponents
  }
}
