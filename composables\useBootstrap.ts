import * as bootstrap from 'bootstrap'

export const useBootstrap = () => {
  const initializeDropdowns = () => {
    if (process.client) {
      nextTick(() => {
        console.log('Inicijalizujem dropdown komponente...')

        // Uni<PERSON>ti postojeće dropdown instance da <PERSON><PERSON><PERSON><PERSON> duplikate
        const existingDropdowns = document.querySelectorAll('[data-bs-toggle="dropdown"]')
        console.log(`Pronašao ${existingDropdowns.length} postojećih dropdown elemenata`)
        existingDropdowns.forEach(element => {
          const existingInstance = bootstrap.Dropdown.getInstance(element)
          if (existingInstance) {
            existingInstance.dispose()
          }
        })

        // Kreiraj nove dropdown instance
        const dropdownElementList = document.querySelectorAll('[data-bs-toggle="dropdown"]')
        console.log(`Kreiram ${dropdownElementList.length} novih dropdown instanci`)

        const dropdownList = [...dropdownElementList].map(dropdownToggleEl => {
          console.log('Kreiram dropdown za element:', dropdownToggleEl)

          // Dodaj eksplicitni event listener za hover i click
          dropdownToggleEl.addEventListener('mouseenter', function() {
            // Hover efekat za desktop
            if (window.innerWidth >= 992) {
              const dropdown = bootstrap.Dropdown.getInstance(this) || new bootstrap.Dropdown(this)
              dropdown.show()
            }
          })

          dropdownToggleEl.addEventListener('mouseleave', function() {
            // Sakrij dropdown kada miš napusti element (samo za desktop)
            if (window.innerWidth >= 992) {
              setTimeout(() => {
                const dropdown = bootstrap.Dropdown.getInstance(this)
                if (dropdown) {
                  dropdown.hide()
                }
              }, 100)
            }
          })

          return new bootstrap.Dropdown(dropdownToggleEl, {
            // Opcije za dropdown
            autoClose: true,
            boundary: 'clippingParents'
          })
        })

        console.log(`Inicijalizovano ${dropdownList.length} dropdown komponenti`)
        return dropdownList
      })
    }
  }

  const initializeCollapses = () => {
    if (process.client) {
      nextTick(() => {
        console.log('Inicijalizujem collapse komponente...')

        // Uništi postojeće collapse instance
        const existingCollapses = document.querySelectorAll('[data-bs-toggle="collapse"]')
        console.log(`Pronašao ${existingCollapses.length} postojećih collapse elemenata`)
        existingCollapses.forEach(element => {
          const existingInstance = bootstrap.Collapse.getInstance(element)
          if (existingInstance) {
            existingInstance.dispose()
          }
        })

        // Kreiraj nove collapse instance
        const collapseElementList = document.querySelectorAll('[data-bs-toggle="collapse"]')
        const collapseList = [...collapseElementList].map(collapseToggleEl => {
          return new bootstrap.Collapse(collapseToggleEl, {
            toggle: false
          })
        })

        console.log(`Inicijalizovano ${collapseList.length} collapse komponenti`)
        return collapseList
      })
    }
  }

  const initializeModals = () => {
    if (process.client) {
      nextTick(() => {
        console.log('Inicijalizujem modal komponente...')

        // Uništi postojeće modal instance
        const existingModals = document.querySelectorAll('[data-bs-toggle="modal"]')
        console.log(`Pronašao ${existingModals.length} postojećih modal elemenata`)
        existingModals.forEach(element => {
          const targetSelector = element.getAttribute('data-bs-target')
          if (targetSelector) {
            const targetElement = document.querySelector(targetSelector)
            if (targetElement) {
              const existingInstance = bootstrap.Modal.getInstance(targetElement)
              if (existingInstance) {
                existingInstance.dispose()
              }
            }
          }
        })

        // Kreiraj nove modal instance
        const modalElementList = document.querySelectorAll('.modal')
        const modalList = [...modalElementList].map(modalEl => {
          return new bootstrap.Modal(modalEl)
        })

        console.log(`Inicijalizovano ${modalList.length} modal komponenti`)
        return modalList
      })
    }
  }

  const initializeAllComponents = () => {
    console.log('Inicijalizujem sve Bootstrap komponente...')
    initializeDropdowns()
    initializeCollapses()
    initializeModals()
  }

  const getDropdownInstance = (element: Element) => {
    return bootstrap.Dropdown.getInstance(element)
  }

  const getCollapseInstance = (element: Element) => {
    return bootstrap.Collapse.getInstance(element)
  }

  const getModalInstance = (element: Element) => {
    return bootstrap.Modal.getInstance(element)
  }

  return {
    bootstrap,
    initializeDropdowns,
    initializeCollapses,
    initializeModals,
    initializeAllComponents,
    getDropdownInstance,
    getCollapseInstance,
    getModalInstance
  }
}
